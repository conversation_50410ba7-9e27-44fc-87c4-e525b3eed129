const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const winston = require('winston');
const cron = require('node-cron');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// 日志配置
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ],
});

// 中间件
app.use(helmet());
app.use(compression());
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use(express.static(path.join(__dirname, '../dist')));

// AI模拟数据库
const aiKnowledgeBase = {
  circuits: {
    'D0196-D3036-D5132P-D5144J': {
      title: '发动机整流罩防冰系统线路图',
      sections: [
        {
          name: 'D0196 → D3036 线路段',
          components: [
            '防冰系统主供电线路 (28VDC，额定电流15A)',
            '温度传感器信号连接 (PT100型，精度±0.1°C)',
            '控制模块通信总线 (ARINC 429，数据速率100kbps)',
            '过流保护电路 (25A断路器，跳闸时间<50ms)',
            '系统状态监控线路 (数字信号，5V TTL)'
          ]
        },
        {
          name: 'D3036 → D5132P 线路段',
          components: [
            '加热元件供电回路 (115VAC, 400Hz，功率2.5kW)',
            '温度监控反馈线路 (4-20mA模拟信号)',
            '故障检测与报警电路 (继电器控制，24VDC)',
            '系统状态指示灯控制 (LED驱动，12VDC)',
            '备用加热回路 (115VAC, 400Hz，功率1.2kW)'
          ]
        }
      ],
      safety: [
        '维修前确保飞机主电源断开并上锁，验证零电位状态',
        '使用防静电手环，工作区域接地良好，接地电阻<10Ω',
        '严格按照工卡顺序执行，不可跳步，每步需双人确认'
      ]
    }
  },
  tools: {
    'W3760-007-20': {
      title: 'W3760-007-20导线维修工具包',
      tools: [
        {
          name: 'M22520/5-01 精密剥线钳',
          specs: 'AWG20导线专用，剥线长度6-8mm精确控制',
          standard: 'MIL-T-22520E',
          calibration: '12个月'
        },
        {
          name: 'MS25036 拼接管套件',
          specs: '镀银铜管，抗腐蚀性能优异',
          standard: 'AWG20导线永久连接',
          calibration: '无需校准'
        }
      ],
      workcards: [
        'SWPM 20-00-15 2.C - 导线处理标准程序',
        'SWPM20-30-12 7.G - 拼接管压接规范',
        'SWPM20-61 2.B - D5132J连接器拆卸程序'
      ]
    }
  }
};

// API路由
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '3.0.0',
    ai_status: 'online'
  });
});

app.get('/api/system-stats', (req, res) => {
  res.json({
    cpu_usage: Math.random() * 100,
    memory_usage: Math.random() * 100,
    active_sessions: Math.floor(Math.random() * 50) + 10,
    ai_queries_today: Math.floor(Math.random() * 500) + 100,
    database_status: 'connected',
    last_update: new Date().toISOString()
  });
});

app.post('/api/ai/query', async (req, res) => {
  const { query, type } = req.body;
  
  // 模拟AI处理延迟
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  let response = {};
  
  if (type === 'circuit' || query.includes('线路图')) {
    response = aiKnowledgeBase.circuits['D0196-D3036-D5132P-D5144J'];
  } else if (type === 'tools' || query.includes('W3760-007-20')) {
    response = aiKnowledgeBase.tools['W3760-007-20'];
  } else {
    response = {
      title: '查询结果',
      message: '抱歉，未找到相关信息。请尝试更具体的查询。',
      suggestions: [
        '发动机防冰系统线路图',
        'W3760-007-20导线维修工具',
        '电气系统故障诊断'
      ]
    };
  }
  
  res.json({
    success: true,
    data: response,
    processing_time: '1.2s',
    confidence: Math.random() * 0.3 + 0.7,
    timestamp: new Date().toISOString()
  });
});

// WebSocket连接处理
io.on('connection', (socket) => {
  logger.info(`用户连接: ${socket.id}`);
  
  socket.emit('system_status', {
    status: 'connected',
    ai_ready: true,
    timestamp: new Date().toISOString()
  });
  
  socket.on('ai_query', async (data) => {
    socket.emit('ai_thinking', { status: 'processing' });
    
    // 模拟AI处理
    setTimeout(() => {
      socket.emit('ai_response', {
        query: data.query,
        response: '这是AI生成的响应...',
        timestamp: new Date().toISOString()
      });
    }, 2000);
  });
  
  socket.on('disconnect', () => {
    logger.info(`用户断开连接: ${socket.id}`);
  });
});

// 定时任务 - 系统健康检查
cron.schedule('*/5 * * * *', () => {
  logger.info('系统健康检查执行');
  io.emit('system_heartbeat', {
    timestamp: new Date().toISOString(),
    status: 'healthy'
  });
});

// 错误处理
app.use((err, req, res, next) => {
  logger.error(err.stack);
  res.status(500).json({
    error: '服务器内部错误',
    timestamp: new Date().toISOString()
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    error: '接口不存在',
    path: req.path,
    timestamp: new Date().toISOString()
  });
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  logger.info(`服务器运行在端口 ${PORT}`);
  console.log(`🚀 航空维修技术支持系统启动成功`);
  console.log(`📡 API服务: http://localhost:${PORT}/api`);
  console.log(`🔌 WebSocket: ws://localhost:${PORT}`);
});

module.exports = app;