<template>
  <div class="circuit-diagrams">
    <div class="page-header">
      <h1 class="gradient-text">线路图查询</h1>
      <p>查询和浏览航空器线路图和电气系统图</p>
    </div>

    <div class="search-section">
      <div class="search-bar">
        <input 
          v-model="searchQuery" 
          type="text" 
          placeholder="输入飞机型号、系统名称或线路编号..."
          class="input"
          @keyup.enter="searchCircuits"
        />
        <button @click="searchCircuits" class="btn btn-primary">
          <span class="material-icons">search</span>
          搜索
        </button>
      </div>
      
      <div class="filter-options">
        <select v-model="selectedAircraft" class="input">
          <option value="">选择飞机型号</option>
          <option value="A320">空客 A320</option>
          <option value="B737">波音 737</option>
          <option value="A330">空客 A330</option>
          <option value="B777">波音 777</option>
        </select>
        
        <select v-model="selectedSystem" class="input">
          <option value="">选择系统</option>
          <option value="electrical">电气系统</option>
          <option value="hydraulic">液压系统</option>
          <option value="fuel">燃油系统</option>
          <option value="avionics">航电系统</option>
        </select>
      </div>
    </div>

    <div class="results-section">
      <div class="results-header">
        <h3>搜索结果 ({{ filteredCircuits.length }})</h3>
        <div class="view-toggle">
          <button 
            @click="viewMode = 'grid'" 
            :class="{ active: viewMode === 'grid' }"
            class="btn btn-secondary"
          >
            <span class="material-icons">grid_view</span>
          </button>
          <button 
            @click="viewMode = 'list'" 
            :class="{ active: viewMode === 'list' }"
            class="btn btn-secondary"
          >
            <span class="material-icons">list</span>
          </button>
        </div>
      </div>

      <div :class="['circuits-container', viewMode]">
        <div 
          v-for="circuit in filteredCircuits" 
          :key="circuit.id"
          class="circuit-card"
          @click="openCircuit(circuit)"
        >
          <div class="circuit-preview">
            <span class="material-icons">account_tree</span>
          </div>
          <div class="circuit-info">
            <h4>{{ circuit.title }}</h4>
            <p class="circuit-description">{{ circuit.description }}</p>
            <div class="circuit-meta">
              <span class="aircraft-type">{{ circuit.aircraft }}</span>
              <span class="system-type">{{ circuit.system }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 线路图查看器模态框 -->
    <div v-if="selectedCircuit" class="circuit-viewer-overlay" @click="closeCircuit">
      <div class="circuit-viewer" @click.stop>
        <div class="viewer-header">
          <h3>{{ selectedCircuit.title }}</h3>
          <button @click="closeCircuit" class="close-btn">×</button>
        </div>
        <div class="viewer-content">
          <div class="circuit-display">
            <div v-if="selectedCircuit.diagramContent" class="circuit-diagram-container">
              <div class="diagram-header">
                <h3>{{ selectedCircuit.title }}</h3>
                <p>{{ selectedCircuit.description }}</p>
              </div>
              <div class="diagram-content">
                <div v-if="selectedCircuit.diagramContent === 'D0196-D3036'" class="circuit-svg-container">
                  <img :src="getCircuitSVG('D0196-D3036')" alt="D0196-D3036线路图" class="circuit-svg-image" />
                </div>
                <div v-else-if="selectedCircuit.diagramContent === 'D3036-D5132P'" class="circuit-svg-container">
                  <img :src="getCircuitSVG('D3036-D5132P')" alt="D3036-D5132P线路图" class="circuit-svg-image" />
                </div>
                <div v-else-if="selectedCircuit.diagramContent === 'D5132P-D5144J'" class="circuit-svg-container">
                  <img :src="getCircuitSVG('D5132P-D5144J')" alt="D5132P-D5144J线路图" class="circuit-svg-image" />
                </div>
              </div>
            </div>
            <div v-else class="placeholder-content">
              <p>线路图显示区域</p>
              <p class="placeholder-text">这里将显示 {{ selectedCircuit.title }} 的详细线路图</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const searchQuery = ref('')
const selectedAircraft = ref('')
const selectedSystem = ref('')
const viewMode = ref('grid')
const selectedCircuit = ref(null)

// 模拟线路图数据
const circuits = ref([
  {
    id: 'D0196-D3036',
    title: '发动机整流罩防冰系统 D0196至D3036',
    description: '发动机整流罩防冰系统第一段线路图，包含防冰控制器和相关线路',
    aircraft: '737',
    system: 'engine',
    diagramContent: 'D0196-D3036'
  },
  {
    id: 'D3036-D5132P',
    title: '发动机整流罩防冰系统 D3036至D5132P',
    description: '发动机整流罩防冰系统第二段线路图，包含中间连接线路',
    aircraft: '737',
    system: 'engine',
    diagramContent: 'D3036-D5132P'
  },
  {
    id: 'D5132P-D5144J',
    title: '发动机整流罩防冰系统 D5132P至D5144J',
    description: '发动机整流罩防冰系统第三段线路图，包含执行器和终端连接',
    aircraft: '737',
    system: 'engine',
    diagramContent: 'D5132P-D5144J'
  },
  {
    id: 4,
    title: '737 主电气系统',
    description: '空客737主电气系统线路图，包含发电机、配电和应急电源',
    aircraft: '737',
    system: 'electrical'
  },
  {
    id: 5,
    title: 'B737 液压系统A',
    description: '波音737液压系统A线路图，包含泵、储压器和执行器',
    aircraft: 'B737',
    system: 'hydraulic'
  }
])

const filteredCircuits = computed(() => {
  return circuits.value.filter(circuit => {
    const matchesSearch = !searchQuery.value || 
      circuit.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      circuit.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    const matchesAircraft = !selectedAircraft.value || circuit.aircraft === selectedAircraft.value
    const matchesSystem = !selectedSystem.value || circuit.system === selectedSystem.value
    
    return matchesSearch && matchesAircraft && matchesSystem
  })
})

const searchCircuits = () => {
  // 搜索逻辑已通过computed属性实现
  console.log('搜索线路图:', searchQuery.value)
}

const openCircuit = (circuit) => {
  selectedCircuit.value = circuit
}

const closeCircuit = () => {
  selectedCircuit.value = null
}

// 获取SVG格式的线路图
const getCircuitSVG = (circuitId) => {
  const svgFilePaths = {
    'D0196-D3036': '/luxian/图片1_svg.svg',
    'D3036-D5132P': '/luxian/图片2_svg.svg',
    'D5132P-D5144J': '/luxian/图片3_svg.svg'
  }

  return svgFilePaths[circuitId] || '/luxian/图片1_svg.svg'
}

onMounted(() => {
  console.log('线路图查询页面已加载')

  // 检查是否有来自AI分析的查询参数
  const route = useRoute()
  const circuitParam = route.query.circuit
  if (circuitParam) {
    // 根据参数查找对应的线路图
    const circuit = circuits.value.find(c => c.id === circuitParam)
    if (circuit) {
      // 自动打开对应的线路图
      selectedCircuit.value = circuit
    }
  }
})
</script>

<style scoped>
.circuit-diagrams {
  padding: 2rem;
  width: 100%;
  min-height: calc(100vh - 120px);
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.search-section {
  margin-bottom: 2rem;
}

.search-bar {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.search-bar input {
  flex: 1;
}

.filter-options {
  display: flex;
  gap: 1rem;
}

.filter-options select {
  min-width: 200px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.view-toggle {
  display: flex;
  gap: 0.5rem;
}

.view-toggle button {
  padding: 0.5rem;
}

.view-toggle button.active {
  background: var(--secondary-blue);
  color: white;
}

.circuits-container.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.circuits-container.list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.circuit-card {
  background: var(--glass-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.circuit-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--secondary-blue);
}

.circuits-container.grid .circuit-card {
  display: flex;
  flex-direction: column;
}

.circuits-container.list .circuit-card {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.circuit-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: var(--secondary-blue);
  border-radius: 8px;
  margin-bottom: 1rem;
  color: white;
}

.circuits-container.list .circuit-preview {
  margin-bottom: 0;
  margin-right: 1rem;
}

.circuit-info h4 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.circuit-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.circuit-meta {
  display: flex;
  gap: 0.5rem;
}

.aircraft-type,
.system-type {
  padding: 0.25rem 0.5rem;
  background: var(--dark-bg);
  border-radius: 4px;
  font-size: 0.75rem;
  color: var(--accent-blue);
}

.circuit-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.circuit-viewer {
  background: var(--card-bg);
  border-radius: 12px;
  width: 90vw;
  height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-xl);
}

.viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.viewer-content {
  flex: 1;
  padding: 1.5rem;
}

.circuit-display {
  width: 100%;
  height: 100%;
  background: var(--dark-bg);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--border-color);
}

.placeholder-text {
  color: var(--text-secondary);
  margin-top: 1rem;
}

.circuit-diagram-container {
  width: 100%;
  height: 100%;
  padding: 2rem;
  overflow: auto;
}

.diagram-header {
  margin-bottom: 2rem;
  text-align: center;
}

.diagram-header h3 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.diagram-content {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.circuit-svg-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: auto;
}

.circuit-svg-container svg {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.placeholder-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.circuit-svg-image {
  max-width: 100%;
  max-height: 80vh;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
