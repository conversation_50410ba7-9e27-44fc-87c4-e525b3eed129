<template>
  <div class="ai-analysis">
    <div class="page-header">
      <h1 class="gradient-text">AI智能分析</h1>
      <p>基于人工智能的故障诊断和维修建议系统</p>
    </div>

    <div class="analysis-layout">
      <!-- 左侧输入区域 -->
      <div class="input-section">
        <div class="card">
          <h3>故障描述</h3>
          <div class="input-group">
            <label>飞机型号</label>
            <select v-model="analysis.aircraft" class="input">
              <option value="">选择飞机型号</option>
              <option value="A320">空客 A320</option>
              <option value="B737">波音 737</option>
              <option value="A330">空客 A330</option>
              <option value="B777">波音 777</option>
            </select>
          </div>
          
          <div class="input-group">
            <label>故障系统</label>
            <select v-model="analysis.system" class="input">
              <option value="">选择系统</option>
              <option value="engine">发动机</option>
              <option value="hydraulic">液压系统</option>
              <option value="electrical">电气系统</option>
              <option value="fuel">燃油系统</option>
              <option value="avionics">航电系统</option>
              <option value="landing-gear">起落架</option>
            </select>
          </div>

          <div class="input-group">
            <label>故障现象</label>
            <textarea 
              v-model="analysis.symptoms" 
              placeholder="请详细描述故障现象、报警信息、异常声音等..."
              class="input textarea"
              rows="4"
            ></textarea>
          </div>

          <div class="input-group">
            <label>环境条件</label>
            <textarea 
              v-model="analysis.conditions" 
              placeholder="飞行阶段、天气条件、温度等环境因素..."
              class="input textarea"
              rows="2"
            ></textarea>
          </div>

          <button @click="analyzeIssue" :disabled="isAnalyzing" class="btn btn-primary full-width">
            <span v-if="isAnalyzing" class="loading-spinner"></span>
            <span class="material-icons" v-else>psychology</span>
            {{ isAnalyzing ? 'AI分析中...' : '开始AI分析' }}
          </button>
        </div>

        <!-- 历史分析记录 -->
        <div class="card">
          <h3>分析历史</h3>
          <div class="history-list">
            <div 
              v-for="record in analysisHistory" 
              :key="record.id"
              class="history-item"
              @click="loadAnalysis(record)"
            >
              <div class="history-header">
                <span class="aircraft-badge">{{ record.aircraft }}</span>
                <span class="date">{{ formatDate(record.date) }}</span>
              </div>
              <div class="history-summary">{{ record.summary }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧结果区域 -->
      <div class="results-section">
        <div v-if="currentAnalysis" class="card">
          <h3>AI分析结果</h3>

          <!-- 线路图直接显示模式 -->
          <div v-if="currentAnalysis.queryType === 'circuit_diagram_direct'" class="circuit-direct-display">
            <div class="circuit-message">
              <span class="material-icons">electrical_services</span>
              <p>{{ currentAnalysis.message }}</p>
            </div>
            <div v-if="currentAnalysis.circuitsFound.length > 1" class="circuit-navigation">
              <h4>可用线路图：</h4>
              <div class="circuit-buttons">
                <button
                  v-for="circuitId in currentAnalysis.circuitsFound"
                  :key="circuitId"
                  @click="viewCircuitDiagram(circuitId)"
                  class="btn btn-primary circuit-nav-btn"
                >
                  {{ getCircuitTitle(circuitId) }}
                </button>
              </div>
            </div>
          </div>

          <!-- 普通分析结果 -->
          <div v-else>
            <!-- 可能原因 -->
            <div class="analysis-block">
              <h4><span class="material-icons">error_outline</span>可能原因</h4>
            <div class="causes-list">
              <div 
                v-for="(cause, index) in currentAnalysis.causes" 
                :key="index"
                class="cause-item"
              >
                <div class="cause-header">
                  <span class="probability">{{ cause.probability }}%</span>
                  <span class="cause-name">{{ cause.name }}</span>
                </div>
                <div class="cause-description">{{ cause.description }}</div>
                <div class="probability-bar">
                  <div class="probability-fill" :style="{ width: cause.probability + '%' }"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 维修工具查询结果 -->
          <div v-if="currentAnalysis.isMaintenanceQuery" class="analysis-block">
            <h4><span class="material-icons">build_circle</span>维修工具和工卡</h4>

            <!-- 故障描述 -->
            <div class="fault-description">
              <h5>故障描述：</h5>
              <p>{{ currentAnalysis.faultDescription }}</p>
            </div>

            <!-- 所需工具 -->
            <div class="tools-section">
              <h5><span class="material-icons">construction</span>所需工具：</h5>
              <div class="tools-grid">
                <div v-for="tool in currentAnalysis.tools" :key="tool" class="tool-item">
                  <span class="material-icons">build</span>
                  {{ tool }}
                </div>
              </div>
            </div>

            <!-- 工具图片展示 -->
            <div class="tools-images-section">
              <h5><span class="material-icons">image</span>工具图片：</h5>
              <div class="tools-images-grid">
                <div v-for="(tool, index) in getToolImages()" :key="index" class="tool-image-card">
                  <div class="tool-image-container">
                    <img :src="tool.image" :alt="tool.name" class="tool-image" />
                  </div>
                  <div class="tool-image-label">{{ tool.name }}</div>
                </div>
              </div>
            </div>

            <!-- 维修工卡 -->
            <div class="workcards-section">
              <h5><span class="material-icons">assignment</span>维修工卡</h5>
              <div class="workcards-content">
                <div class="workcard-row">
                  <span class="workcard-name">剥线工卡</span>
                  <span class="workcard-code">SWPM 20-00-15</span>
                  <span class="workcard-section">2.C</span>
                </div>
                <div class="workcard-row">
                  <span class="workcard-name">压接工卡</span>
                  <span class="workcard-code">SWPM20-30-12</span>
                  <span class="workcard-section">7.G</span>
                </div>
                <div class="workcard-row">
                  <span class="workcard-name">移除连接器工卡</span>
                  <span class="workcard-code">SWPM20-61</span>
                  <span class="workcard-section">2.B</span>
                </div>
                <div class="workcard-row">
                  <span class="workcard-name">连接器压接工卡</span>
                  <span class="workcard-code">SWPM20-61</span>
                  <span class="workcard-section">3.A</span>
                </div>
                <div class="workcard-row">
                  <span class="workcard-name">连接器安装工卡</span>
                  <span class="workcard-code">SWPM20-61</span>
                  <span class="workcard-section">3.B</span>
                </div>
                <div class="workcard-row">
                  <span class="workcard-name">系带线束扎带的组装-高振动区域工卡</span>
                  <span class="workcard-code">20-10-11</span>
                  <span class="workcard-section">4D</span>
                </div>
              </div>
            </div>

            <!-- 技术参考表格 -->
            <div class="technical-tables-section">
              <h5><span class="material-icons">table_chart</span>技术参考表格：</h5>
              <div class="tables-grid">
                <div
                  v-for="table in currentAnalysis.technicalTables"
                  :key="table.id"
                  class="table-card"
                  @click="viewTechnicalTable(table.id)"
                >
                  <span class="material-icons">description</span>
                  <span>{{ table.name }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 线路图显示 (如果是线路图查询) -->
          <div v-if="currentAnalysis.isCircuitQuery" class="analysis-block">
            <h4><span class="material-icons">electrical_services</span>线路图</h4>
            <div class="circuit-diagrams-grid">
              <div
                v-for="(rec, index) in currentAnalysis.recommendations"
                :key="index"
                class="circuit-diagram-card"
                v-if="rec.action === 'view_circuit'"
              >
                <div class="circuit-card-header">
                  <h5>{{ rec.title.replace('查看线路图 ', '') }}</h5>
                  <p>{{ rec.description }}</p>
                </div>
                <div class="circuit-card-image">
                  <img
                    :src="getCircuitImage(rec.circuitId)"
                    :alt="rec.title"
                    class="circuit-preview-image"
                    @click="viewCircuitDiagram(rec.circuitId)"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 维修建议 (如果不是线路图查询) -->
          <div v-if="!currentAnalysis.isCircuitQuery" class="analysis-block">
            <h4><span class="material-icons">build</span>维修建议</h4>
            <div class="recommendations">
              <div
                v-for="(rec, index) in currentAnalysis.recommendations"
                :key="index"
                class="recommendation-item"
              >
                <div class="rec-priority" :class="rec.priority">{{ rec.priority }}</div>
                <div class="rec-content">
                  <div class="rec-title">{{ rec.title }}</div>
                  <div class="rec-description">{{ rec.description }}</div>
                  <div class="rec-time">预计时间: {{ rec.estimatedTime }}</div>
                </div>
                <button
                  v-if="rec.action === 'view_circuit'"
                  @click="viewCircuitDiagram(rec.circuitId)"
                  class="btn btn-primary btn-sm"
                >
                  查看线路图
                </button>
              </div>
            </div>
          </div>

            <!-- 相关文档 -->
            <div v-if="!currentAnalysis.isCircuitQuery || currentAnalysis.queryType !== 'circuit_diagram_direct'" class="analysis-block">
              <h4><span class="material-icons">description</span>相关文档</h4>
              <div class="documents-list">
                <div
                  v-for="doc in currentAnalysis.documents"
                  :key="doc.id"
                  class="document-item"
                >
                  <span class="material-icons">{{ doc.type === 'manual' ? 'book' : 'description' }}</span>
                  <div class="doc-info">
                    <div class="doc-title">{{ doc.title }}</div>
                    <div class="doc-reference">{{ doc.reference }}</div>
                  </div>
                  <button class="btn btn-secondary">查看</button>
                </div>
              </div>
            </div>

            <!-- 安全注意事项 -->
            <div v-if="!currentAnalysis.isCircuitQuery || currentAnalysis.queryType !== 'circuit_diagram_direct'" class="analysis-block safety-block">
              <h4><span class="material-icons">warning</span>安全注意事项</h4>
              <div class="safety-warnings">
                <div
                  v-for="warning in currentAnalysis.safetyWarnings"
                  :key="warning"
                  class="safety-warning"
                >
                  <span class="material-icons">warning</span>
                  {{ warning }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="card placeholder-card">
          <div class="placeholder-content">
            <span class="material-icons">psychology</span>
            <h3>AI智能分析</h3>
            <p>请在左侧输入故障信息，AI将为您提供专业的诊断和维修建议</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 线路图显示模态框 -->
    <div v-if="showCircuitDiagram" class="circuit-modal" @click="closeCircuitDiagram">
      <div class="circuit-modal-content" @click.stop>
        <div class="circuit-modal-header">
          <h3>{{ getCircuitTitle(selectedCircuitId) }}</h3>
          <button @click="closeCircuitDiagram" class="close-btn">
            <span class="material-icons">close</span>
          </button>
        </div>
        <div class="circuit-modal-body">
          <img
            :src="getCircuitImage(selectedCircuitId)"
            :alt="getCircuitTitle(selectedCircuitId)"
            class="circuit-image"
          />
        </div>
      </div>
    </div>

    <!-- 技术表格显示模态框 -->
    <div v-if="showTechnicalTables" class="circuit-modal" @click="closeTechnicalTables">
      <div class="circuit-modal-content" @click.stop>
        <div class="circuit-modal-header">
          <h3>{{ getTechnicalTableTitle(selectedTableId) }}</h3>
          <button @click="closeTechnicalTables" class="close-btn">
            <span class="material-icons">close</span>
          </button>
        </div>
        <div class="circuit-modal-body">
          <img
            :src="getTechnicalTableImage(selectedTableId)"
            :alt="getTechnicalTableTitle(selectedTableId)"
            class="circuit-image"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const analysis = ref({
  aircraft: '',
  system: '',
  symptoms: '',
  conditions: ''
})

const isAnalyzing = ref(false)
const currentAnalysis = ref(null)
const showCircuitDiagram = ref(false)
const selectedCircuitId = ref('')
const showTechnicalTables = ref(false)
const selectedTableId = ref('')

const analysisHistory = ref([
  {
    id: 1,
    aircraft: 'A320',
    system: 'engine',
    summary: '发动机振动异常分析',
    date: new Date('2024-01-15')
  },
  {
    id: 2,
    aircraft: 'B737',
    system: 'hydraulic',
    summary: '液压系统压力下降',
    date: new Date('2024-01-10')
  }
])

const analyzeIssue = async () => {
  if (!analysis.value.symptoms) {
    alert('请填写故障现象或查询内容')
    return
  }

  isAnalyzing.value = true

  // 模拟AI分析过程
  await new Promise(resolve => setTimeout(resolve, 3000))

  // 检查是否是线路图查询
  const isCircuitQuery = analysis.value.symptoms.toLowerCase().includes('线路图') ||
                        analysis.value.symptoms.includes('D0196') ||
                        analysis.value.symptoms.includes('D406') ||
                        analysis.value.symptoms.includes('D5132P') ||
                        analysis.value.symptoms.includes('D5144J')

  // 检查是否为维修工具查询
  const isMaintenanceQuery = analysis.value.symptoms.toLowerCase().includes('维修工具') ||
                            analysis.value.symptoms.toLowerCase().includes('工卡') ||
                            analysis.value.symptoms.toLowerCase().includes('导线断线') ||
                            analysis.value.symptoms.toLowerCase().includes('连接销钉损坏') ||
                            analysis.value.symptoms.includes('W3760-007-20') ||
                            analysis.value.symptoms.includes('D5132J')

  if (isMaintenanceQuery) {
    // 维修工具查询
    currentAnalysis.value = {
      isMaintenanceQuery: true,
      queryType: 'maintenance_tools',
      faultDescription: 'W3760-007-20导线断线且D5132J连接销钉损坏',
      tools: [
        '剥线钳',
        '拼接管套件',
        '压接钳',
        '连接器移除工具',
        '连接器件号',
        '压接工具',
        '送钉工具'
      ],
      workcards: [
        { code: 'SWPM 20-00-15', section: '2.C', description: '剥线工卡' },
        { code: 'SWPM20-30-12', section: '7.G', description: '压接工卡' },
        { code: 'SWPM20-61', section: '2.B', description: '移除连接器工卡' },
        { code: 'SWPM20-61', section: '3.A', description: '连接器压接工卡' },
        { code: 'SWPM20-61', section: '3.B', description: '连接器安装工卡' }
      ],
      technicalTables: [
        { id: 'wire-insulation', name: '绝缘层移除工具表' },
        { id: 'splice-assembly', name: '拼接组件表' },
        { id: 'crimp-tools', name: '压接工具表' },
        { id: 'contact-removal', name: '接触件移除工具表' },
        { id: 'contact-parts', name: '标准接触件部件号表' },
        { id: 'contact-crimp', name: '接触件压接工具表' },
        { id: 'contact-insertion', name: '接触件插入工具表' },
        { id: 'maintenance-procedures', name: '维修程序参考表' }
      ]
    }

    // 显示技术表格
    showTechnicalTables.value = true
  } else if (isCircuitQuery) {
    // 直接显示线路图，不显示分析结果
    const queryText = analysis.value.symptoms.toLowerCase()

    // 检查查询中包含哪些线路图
    const circuitsToShow = []
    if (queryText.includes('d0196') || queryText.includes('d406')) {
      circuitsToShow.push('D0196-D406')
    }
    if (queryText.includes('d406') || queryText.includes('d5132p')) {
      circuitsToShow.push('D406-D5132P')
    }
    if (queryText.includes('d5132p') || queryText.includes('d5144j')) {
      circuitsToShow.push('D5132P-D5144J')
    }

    // 如果没有特定线路图，显示所有三个
    if (circuitsToShow.length === 0) {
      circuitsToShow.push('D0196-D406', 'D406-D5132P', 'D5132P-D5144J')
    }

    // 直接显示第一个线路图
    if (circuitsToShow.length > 0) {
      selectedCircuitId.value = circuitsToShow[0]
      showCircuitDiagram.value = true
    }

    // 设置一个简单的结果显示
    currentAnalysis.value = {
      isCircuitQuery: true,
      queryType: 'circuit_diagram_direct',
      circuitsFound: circuitsToShow,
      message: `已找到 ${circuitsToShow.length} 个相关线路图，正在显示第一个。`
    }
  } else {
    // 普通故障分析结果
    currentAnalysis.value = {
      isCircuitQuery: false,
      queryType: 'fault_analysis',
      causes: [
        {
          name: '燃油泵故障',
          probability: 85,
          description: '燃油泵内部磨损或电气故障导致供油不足'
        },
        {
          name: '燃油滤清器堵塞',
          probability: 65,
          description: '燃油滤清器长期使用导致堵塞，影响燃油流量'
        },
        {
          name: '燃油管路泄漏',
          probability: 45,
          description: '燃油管路连接处或管路本身出现泄漏'
        }
      ],
      recommendations: [
        {
          priority: 'high',
          title: '检查燃油泵',
          description: '使用万用表检查燃油泵电气连接，测试泵的工作电流',
          estimatedTime: '2小时'
        },
        {
          priority: 'medium',
          title: '更换燃油滤清器',
          description: '按照维修手册要求更换燃油滤清器',
          estimatedTime: '1小时'
        },
        {
          priority: 'low',
          title: '检查燃油管路',
          description: '目视检查燃油管路是否有泄漏迹象',
          estimatedTime: '30分钟'
        }
      ],
      documents: [
        {
          id: 1,
          type: 'manual',
          title: 'A320 燃油系统维修手册',
          reference: 'AMM 28-21-00'
        },
        {
          id: 2,
          type: 'bulletin',
          title: '燃油泵故障排除指南',
          reference: 'SB A320-28-1234'
        }
      ],
      safetyWarnings: [
        '维修前确保燃油系统已完全泄压',
        '使用防爆工具进行维修作业',
        '确保工作区域通风良好，远离火源'
      ]
    }
  }

  // 添加到历史记录
  analysisHistory.value.unshift({
    id: Date.now(),
    aircraft: analysis.value.aircraft,
    system: analysis.value.system,
    summary: `${analysis.value.system}系统故障分析`,
    date: new Date()
  })

  isAnalyzing.value = false
}

const loadAnalysis = (record) => {
  // 加载历史分析记录
  console.log('加载分析记录:', record)
}

const formatDate = (date) => {
  return date.toLocaleDateString('zh-CN')
}

const viewCircuitDiagram = (circuitId) => {
  // 直接显示线路图
  selectedCircuitId.value = circuitId
  showCircuitDiagram.value = true
}

const closeCircuitDiagram = () => {
  showCircuitDiagram.value = false
  selectedCircuitId.value = ''
}

// UTF-8 安全的 base64 编码函数
const utf8ToBase64 = (str) => {
  // 使用 TextEncoder 来处理 UTF-8 编码
  const encoder = new TextEncoder()
  const data = encoder.encode(str)
  let binary = ''
  for (let i = 0; i < data.length; i++) {
    binary += String.fromCharCode(data[i])
  }
  return btoa(binary)
}

// 线路图数据 - 使用外部SVG文件
const circuitData = {
  'D0196-D406': {
    title: '发动机整流罩防冰系统 D0196至D406',
    image: '/luxian/图片1_svg.svg'
  },
  'D406-D5132P': {
    title: '发动机整流罩防冰系统 D406至D5132P',
    image: '/luxian/图片2_svg.svg?v=' + Date.now()
  },
  'D5132P-D5144J': {
    title: '发动机整流罩防冰系统 D5132P至D5144J',
    image: '/luxian/图片3_svg.svg'
  }
}

const getCircuitTitle = (circuitId) => {
  return circuitData[circuitId]?.title || '线路图'
}

const getCircuitImage = (circuitId) => {
  return circuitData[circuitId]?.image || ''
}

// 获取工具图片
const getToolImages = () => {
  return [
    {
      name: '剥线钳',
      image: '/tupian/剥线钳.jpg'
    },
    {
      name: '拼接管套件',
      image: '/tupian/拼接管套件.jpg'
    },
    {
      name: '压接钳',
      image: '/tupian/压接钳.jpg'
    },
    {
      name: '连接器移除工具',
      image: '/tupian/连接器移除工具.jpg'
    },
    {
      name: '连接器件号',
      image: '/tupian/连接器（销钉）.jpg'
    },
    {
      name: '压接工具',
      image: '/tupian/销钉压接钳.jpg'
    },
    {
      name: '送钉工具',
      image: '/tupian/连接器安装工具.jpg'
    }
  ]
}

// 技术表格相关函数
const viewTechnicalTable = (tableId) => {
  selectedTableId.value = tableId
  showTechnicalTables.value = true
}

const closeTechnicalTables = () => {
  showTechnicalTables.value = false
  selectedTableId.value = ''
}

const getTechnicalTableTitle = (tableId) => {
  const tableMap = {
    'wire-insulation': '绝缘层移除工具表',
    'splice-assembly': '拼接组件表',
    'crimp-tools': '压接工具表',
    'contact-removal': '接触件移除工具表',
    'contact-parts': '标准接触件部件号表',
    'contact-crimp': '接触件压接工具表',
    'contact-insertion': '接触件插入工具表',
    'maintenance-procedures': '维修程序参考表'
  }
  return tableMap[tableId] || '技术表格'
}

const getTechnicalTableImage = (tableId) => {
  // 返回对应的SVG文件路径
  const imageMap = {
    'wire-insulation': '/gongju/图片1_svg.svg',
    'splice-assembly': '/gongju/图片2_svg.svg?v=' + Date.now(),
    'crimp-tools': '/gongju/图片3_svg.svg',
    'contact-removal': '/gongju/图片4_svg.svg',
    'contact-parts': '/gongju/图片5_svg.svg',
    'contact-crimp': '/gongju/图片6_svg.svg',
    'contact-insertion': '/gongju/图片7_svg.svg',
    'maintenance-procedures': '/gongju/图片8_svg.svg'
  }
  return imageMap[tableId] || '/gongju/图片1_svg.svg'
}

onMounted(() => {
  console.log('AI分析页面已加载')
})
</script>

<style scoped>
.ai-analysis {
  padding: 2rem;
  width: 100%;
  min-height: calc(100vh - 120px);
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.analysis-layout {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 2rem;
}

.input-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.input-group {
  margin-bottom: 1rem;
}

.input-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

.full-width {
  width: 100%;
  justify-content: center;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-item:hover {
  background: var(--dark-bg);
  border-color: var(--secondary-blue);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.aircraft-badge {
  background: var(--secondary-blue);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
}

.date {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.history-summary {
  font-size: 0.875rem;
  color: var(--text-primary);
}

.analysis-block {
  margin-bottom: 2rem;
}

.analysis-block h4 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--accent-blue);
  margin-bottom: 1rem;
}

.cause-item {
  background: var(--dark-bg);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.75rem;
}

.cause-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.probability {
  background: var(--secondary-blue);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: bold;
}

.cause-name {
  font-weight: 600;
  color: var(--text-primary);
}

.cause-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.probability-bar {
  height: 4px;
  background: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
}

.probability-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--secondary-blue), var(--accent-blue));
  transition: width 0.3s ease;
}

.recommendation-item {
  display: flex;
  gap: 1rem;
  background: var(--dark-bg);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.75rem;
}

.rec-priority {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: bold;
  height: fit-content;
}

.rec-priority.high {
  background: var(--danger-color);
  color: white;
}

.rec-priority.medium {
  background: var(--warning-color);
  color: white;
}

.rec-priority.low {
  background: var(--success-color);
  color: white;
}

.rec-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.rec-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.rec-time {
  color: var(--accent-blue);
  font-size: 0.75rem;
}

.document-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: var(--dark-bg);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.5rem;
}

.doc-info {
  flex: 1;
}

.doc-title {
  font-weight: 600;
  color: var(--text-primary);
}

.doc-reference {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.safety-block {
  border: 2px solid var(--warning-color);
  border-radius: 8px;
  padding: 1rem;
  background: rgba(245, 158, 11, 0.1);
}

.safety-warning {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--warning-color);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.placeholder-card {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-content {
  text-align: center;
  color: var(--text-secondary);
}

.placeholder-content .material-icons {
  font-size: 4rem;
  margin-bottom: 1rem;
  color: var(--border-color);
}

@media (max-width: 1024px) {
  .analysis-layout {
    grid-template-columns: 1fr;
  }
}

/* 线路图模态框样式 */
.circuit-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.circuit-modal-content {
  background: white;
  border-radius: 12px;
  max-width: 95vw;
  max-height: 95vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.circuit-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  background: var(--primary-color);
  color: white;
}

.circuit-modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: white;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.circuit-modal-body {
  padding: 2rem;
  max-height: 80vh;
  overflow: auto;
}

.circuit-image {
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.circuit-preview-image {
  max-width: 100%;
  max-height: 200px;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.circuit-preview-image:hover {
  transform: scale(1.05);
}

/* 线路图直接显示样式 */
.circuit-direct-display {
  text-align: center;
  padding: 2rem;
}

.circuit-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--dark-bg);
  border-radius: 8px;
  border-left: 4px solid var(--accent-blue);
}

.circuit-message .material-icons {
  font-size: 2rem;
  color: var(--accent-blue);
}

.circuit-message p {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.circuit-navigation {
  margin-top: 1.5rem;
}

.circuit-navigation h4 {
  color: var(--accent-blue);
  margin-bottom: 1rem;
}

.circuit-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-width: 500px;
  margin: 0 auto;
}

.circuit-nav-btn {
  padding: 1rem 1.5rem;
  font-size: 0.95rem;
  text-align: left;
  justify-content: flex-start;
}

.circuit-nav-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 工具图片样式 */
.tools-images-section {
  margin-top: 2rem;
}

.tools-images-section h5 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-weight: 600;
}

.tools-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.tool-image-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

.tool-image-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--secondary-blue);
}

.tool-image-container {
  margin-bottom: 0.75rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tool-image-container .tool-image {
  max-width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tool-image-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

/* 工具图片样式 */
.tools-images-section {
  margin-top: 2rem;
}

.tools-images-section h5 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-weight: 600;
}

.tools-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.tool-image-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

.tool-image-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--secondary-blue);
}

.tool-image-container {
  margin-bottom: 0.75rem;
  display: flex;
  justify-content: center;
  align-items: center;
}



.tool-image-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

/* 工卡样式 */
.workcards-section {
  margin-top: 2rem;
}

.workcards-section h5 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-weight: 600;
}

.workcards-content {
  background: var(--dark-bg);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--border-color);
}

.workcard-row {
  display: grid;
  grid-template-columns: 2fr 1fr 0.5fr;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
  align-items: center;
}

.workcard-row:last-child {
  border-bottom: none;
}

.workcard-name {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.9rem;
}

.workcard-code {
  color: var(--accent-blue);
  font-family: 'Courier New', monospace;
  font-weight: 600;
  font-size: 0.85rem;
  background: rgba(59, 130, 246, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  text-align: center;
}

.workcard-section {
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 0.85rem;
  text-align: center;
  background: var(--secondary-blue);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}
</style>
